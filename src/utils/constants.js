import { AGGREGATOR_API } from '../config/urlConfig';

export const SW = true;

export const API_TIMEOUT = 30000; // 30s

export const PRODUCT = 'EQUITY';

export const SUB_PRODUCT = 'CASH';

export const VERTICAL_NAME = 'Paytm Stocks';

export const GENERIC_ERROR_MSG = 'Oops, something went wrong.';

export const NO_DATA_FOUND = 'No data found. Please try again';

export const EMPTY_SCREEN = {
  NO_INTERNET: {
    MESSAGE: 'No internet',
    SUB_MESSAGE: 'Please check your network settings',
    RETRY: 'Refresh',
  },
  ERROR: {
    MESSAGE: 'Something Went Wrong',
    SUB_MESSAGE: 'Please check the internet settings',
    RETRY: 'Refresh',
  },
  ERROR_LOGIN: {
    MESSAGE: 'Something Went Wrong',
    SUB_MESSAGE: 'Your session has expired. Please log in to continue.',
    GO_BACK: 'Go Back',
  },
};

export const CONNECTION_MODE = {
  ONLINE: 'online',
  OFFLINE: 'offline',
};

export const IR_STATUS_ENUM = {
  DEFAULT: 'DEFAULT',
  PENDING: 'PENDING',
  LOCKED: 'LOCKED',
  VERIFIED: 'VERIFIED',
  ACTIVE: 'ACTIVE',
  SUBMITTED: 'SUBMITTED',
  REJECTED: 'REJECTED',
  INVERIFICATION: 'INVERIFICATION',
  REVOKED: 'REVOKED',
  NOT_OPTED: 'NOT_OPTED',
  AADHAR_NOT_SEEDED_PAN_REVOKED: 'AADHAR_NOT_SEEDED_PAN_REVOKED',
  DORMANT_REVOKED: 'DORMANT_REVOKED',
  DORMANCY_IN_PROGRESS: 'DORMANCY_IN_PROGRESS',
  REKYC_IN_PROGRESS: 'REKYC_IN_PROGRESS',
  DOCUMENTS_SUBMITTED: 'DOCUMENTS_SUBMITTED',
  DOCUMENTS_PENDING: 'DOCUMENTS_PENDING',
};

export const SNACKBAR_DISMISS_TIME = 5000;

export const TOAST_DISMISS_TIME = 2000;

export const APPEARANCE_TYPES = {
  SUCCESS: 'positive',
  FAIL: 'negative',
  INFO: 'notice',
  PRIMARY: 'primary',
};

export const API_ERROR = {
  MESSAGE: 'Something Went Wrong!',
  ERROR_CODE: (code) => `Error code: ${code}`,
  OK: 'OK',
};

export const API_ERROR_CODES = {
  code: 'code',
  error_code: 'error_code',
  oms_error_code: 'oms_error_code',
};

export const COOKIES = {
  SSO_TOKEN: 'x-sso-token',
  USER_AGENT: 'x-user-agent',
  UID: 'x-uid',
  AUTHORIZATION: 'x-authorization',
  TWO_FA_TOKEN: 'x-2fa-token',
};

export const HAS_INVESTED_STATUS = {
  NOT_INVESTED: 'NOTINVESTED',
  IN_PROGRESS: 'IN_PROGRESS',
  HAS_INVESTED: 'HASINVESTED',
  REDEEMED_ALL: 'REDEEMED_ALL',
};

export const STATUS = {
  DEFAULT: 'DEFAULT',
  VERIFIED: 'VERIFIED',
  PENDING: 'PENDING',
  INVERIFICATION: 'INVERIFICATION',
  REJECTED: 'REJECTED',
  LOCKED: 'LOCKED',
  SUBMITTED: 'SUBMITTED',
  REVOKED: 'REVOKED',
  IR_ACTIVE: 'ACTIVE',
  DORMANT_REVOKED: 'DORMANT_REVOKED',
  DORMANCY_IN_PROGRESS: 'DORMANCY_IN_PROGRESS',
  REKYC_IN_PROGRESS: 'REKYC_IN_PROGRESS',
};

export const MAIN_BUCKET_KEYS = {
  EQ_KYC_DIGILOCKER: 'EQ_KYC_DIGILOCKER',
  EQ_KYC_PERSONAL: 'EQ_KYC_PERSONAL',
  EQ_ONB: 'EQ_ONB',
  EQ_AOF: 'EQ_AOF',
  EQ_KYC: 'EQ_KYC',
};

export const PULSE_STATICS_FIRST_CARDS = {
  VERTICAL_NAME: 'homescreen_4.0',
  SCREEN_NAME: '/homescreen',
  CATEGORY: 'homescreen',
};

export const PULSE_STATICS_DAILY_SIP = {
  VERTICAL_NAME: 'homescreen_4.0',
  SCREEN_NAME: '/homescreen',
  CATEGORY: 'dailysip_widget_combineddashboard',
  CATEGORY_MF: 'dailysip_widget_mfdashboard',
};

export const PULSE_STATICS_MONTHLY_SIP = {
  VERTICAL_NAME: 'homescreen_4.0',
  SCREEN_NAME: '/homescreen',
  CATEGORY: 'monthlysip_widget_combineddashboard',
  CATEGORY_MF: 'dailysip_widget_mfdashboard',
};

export const PULSE_STATICS_ETF_CARD = {
  VERTICAL_NAME: 'homescreen_4.0',
  SCREEN_NAME: '/homescreen',
  CATEGORY: 'homescreen',
};

export const PULSE_EVENTS = {
  event: 'custom_event',
  verticalName: 'pm_mini_app',
  actions: {},
  OPEN_SCREEN: 'openScreen',
};

export const BUFFER = 0.03;

export const EXCHANGE_TYPE = {
  NSE: 'NSE',
  BSE: 'BSE',
};

export const SHIMMER_TYPE = {
  STOCK_SHIMMER: 'stock_shimmer',
  EMPTY_SCREEN: 'empty_screen',
  STOCK_THEME: 'stock_theme',
  ROUND_BUTTONS: 'round_buttons',
  ORDER_CONFIRMATION: 'order_confirmation',
};

export const mfH5Deeplink = {
  aid: '057cafb87da3420eb54f1416f8dc9e7e',
  params: '?stsBarHt=true&origin=paytm',
  path: '',
  sparams: { showTitleBar: false, canPullDown: false },
  // data: {
  //   params: '?stsBarHt=true&origin=paytm',
  //   path: 'auto-pay',
  //   sparams: { showTitleBar: false, canPullDown: false },
  // },
};

export const fundSelectionDeeplink =
  'paytmmoney:///mini-app?aId=7398c9dbed384124b1a45a9e9bd1d0dc&pageName=fundselection-popup&phoenixPopup=true';

export const dailySipDisclaimerDeeplink =
  'paytmmoney:///mini-app?aId=7398c9dbed384124b1a45a9e9bd1d0dc&pageName=mf-daily-sip-disclaimer&phoenixPopup=true';

export const amountSelectionDeeplink =
  'paytmmoney:///mini-app?aId=7398c9dbed384124b1a45a9e9bd1d0dc&pageName=mf-daily-sip-select-amount&phoenixPopup=true';

export const mfH5DeeplinkPML =
  'paytmmoney:///mini-app?aId=057cafb87da3420eb54f1416f8dc9e7e&pageName=';

export const etfSelectionDeeplink =
  'paytmmoney:///mini-app?aId=7398c9dbed384124b1a45a9e9bd1d0dc&pageName=etf-selection-popup&phoenixPopup=true';

export const etfThemedSelectionDeeplink =
  'paytmmoney:///mini-app?aId=7398c9dbed384124b1a45a9e9bd1d0dc&pageName=etf-themed-selection-popup&phoenixPopup=true';

export const reminderWidgetListDeeplink =
  'paytmmoney:///mini-app?aId=7398c9dbed384124b1a45a9e9bd1d0dc&pageName=reminder-widget-list';
export const newsWidgetListDeeplink =
  'paytmmoney:///mini-app?aId=7398c9dbed384124b1a45a9e9bd1d0dc&pageName=news-feed-widget-drawer&phoenixPopup=true';
export const viewAllNewsListDeeplink =
  'paytmmoney:///mini-app?aId=7398c9dbed384124b1a45a9e9bd1d0dc&pageName=all-news-feed-widget-drawer';

export const BUSINESS_TYPE_MAPPINGS = {
  STOCKS: {
    refresh: 'refreshStocksHome',
    removeFragment: 'removeH5FragmentStocksHome',
    height: 'stocksHomeH5FragmentHeight',
    aggrUrl: AGGREGATOR_API.STOCKS_DASHBOARD,
    aggrFallbackUrl: AGGREGATOR_API.STOCKS_DASHBOARD_FALLBACK,
  },
  MUTUAL_FUND: {
    refresh: 'refreshMFHome',
    removeFragment: 'removeH5FragmentMFHome',
    height: 'mfHomeH5FragmentHeight',
    aggrUrl: AGGREGATOR_API.MF_DASHBOARD,
    aggrFallbackUrl: AGGREGATOR_API.COMBINED_DASHBOARD_FALLBACK,
  },
  FNO: {
    refresh: 'refreshFNOHome',
    removeFragment: 'removeH5FragmentFNOHome',
    height: 'fnoHomeH5FragmentHeight',
    aggrUrl: AGGREGATOR_API.FNO_DASHBOARD,
    aggrFallbackUrl: AGGREGATOR_API.FNO_DASHBOARD_FALLBACK,
  },
  ETF: {
    refresh: 'refreshETFHome',
    removeFragment: 'removeH5FragmentETFHome',
    height: 'etfHomeH5FragmentHeight',
    aggrUrl: AGGREGATOR_API.ETF,
    aggrFallbackUrl: AGGREGATOR_API.ETF_FALLBACK,
  },
  IPO: {
    refresh: 'ipoHomeH5FragmentHeight',
    removeFragment: 'removeH5FragmentIPOHome',
    height: 'ipoHomeH5FragmentHeight',
    aggrUrl: AGGREGATOR_API.IPO,
    aggrFallbackUrl: AGGREGATOR_API.IPO_FALLBACK,
  },
  HOMESCREEN: {
    refresh: 'refreshCombinedHome',
    removeFragment: 'removeH5FragmentCombinedHome',
    height: 'combinedHomeH5FragmentHeight',
    aggrUrl: AGGREGATOR_API.COMBINED_DASHBOARD,
    aggrFallbackUrl: AGGREGATOR_API.COMBINED_DASHBOARD_FALLBACK,
  },
};

export const NEWS_WIDGET_ATTRIBUTES = {
  TOP_NEWS: 'NEWS_WIDGET_TOP_NEWS',
  TITLE: 'NEWS_WIDGET_TITLE',
  VIEW_ALL_TITLE: 'NEWS_WIDGET_VIEW_ALL',
};
